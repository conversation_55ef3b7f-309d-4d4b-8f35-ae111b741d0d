const path = require('path');
const chalk = require('chalk');
const MigrationPhase = require('../migration/MigrationPhase');
const RuntimeErrorHandler = require('./RuntimeErrorHandler');
const BuildExecutor = require('../build/BuildExecutor');

/**
 * RuntimeErrorRepairPhase - 运行时错误修复阶段
 *
 * 功能：
 * 1. 启动开发服务器并注入错误监控代码
 * 2. 监听运行时错误并自动修复
 * 3. 提供错误统计和修复报告
 * 4. 支持热重载和实时修复
 */
class RuntimeErrorRepairPhase extends MigrationPhase {
  constructor(projectPath, options = {}) {
    super('运行时错误修复', projectPath, options);

    this.projectPath = projectPath;
    this.options = {
      port: 3000,
      timeout: 30000, // 30秒监控时间
      maxErrors: 50,
      autoFix: true,
      verbose: false,
      ...options
    };

    // 运行时错误处理器
    this.runtimeErrorHandler = new RuntimeErrorHandler(projectPath, {
      port: this.options.port,
      maxErrors: this.options.maxErrors,
      autoFix: this.options.autoFix,
      verbose: this.options.verbose
    });

    // 构建执行器
    this.buildExecutor = new BuildExecutor(projectPath, {
      devCommand: this.options.devCommand || 'npm run dev',
      devTimeout: this.options.timeout,
      verbose: this.options.verbose
    });

    // 错误统计
    this.stats = {
      totalErrors: 0,
      fixedErrors: 0,
      pendingErrors: 0,
      startTime: null,
      endTime: null
    };
  }

  /**
   * 执行运行时错误修复阶段
   */
  async execute() {
    try {
      this.stats.startTime = Date.now();

      console.log(chalk.blue('🚀 启动运行时错误监控...'));

      // 1. 检查项目配置
      await this.checkProjectConfiguration();

      // 2. 注入错误监控代码
      await this.injectErrorMonitoring();

      // 3. 启动开发服务器
      const devServer = await this.startDevServer();

      // 4. 监控运行时错误
      const monitoringResult = await this.monitorRuntimeErrors();

      // 5. 停止开发服务器
      await this.stopDevServer(devServer);

      // 6. 生成修复报告
      const report = this.generateReport();

      this.stats.endTime = Date.now();

      return {
        success: true,
        stats: this.stats,
        report,
        ...monitoringResult
      };

    } catch (error) {
      console.error(chalk.red(`运行时错误修复失败: ${error.message}`));
      return {
        success: false,
        error: error.message,
        stats: this.stats
      };
    }
  }

  /**
   * 检查项目配置
   */
  async checkProjectConfiguration() {
    console.log(chalk.gray('  📋 检查项目配置...'));

    // 检查package.json中的dev命令
    const packageJsonPath = path.join(this.projectPath, 'package.json');
    const packageJson = require(packageJsonPath);

    if (!packageJson.scripts || !packageJson.scripts.dev) {
      throw new Error('项目缺少 dev 启动脚本');
    }

    // 检查Vue版本
    const vueVersion = packageJson.dependencies?.vue || packageJson.devDependencies?.vue;
    if (!vueVersion) {
      throw new Error('项目中未找到Vue依赖');
    }

    console.log(chalk.gray(`  ✅ 项目配置检查完成 (Vue ${vueVersion})`));
  }

  /**
   * 注入错误监控代码
   */
  async injectErrorMonitoring() {
    console.log(chalk.gray('  🔧 注入运行时错误监控代码...'));

    // 生成注入脚本
    const injectionScript = this.runtimeErrorHandler.generateInjectionCode();

    // 创建临时注入文件
    const fs = require('fs-extra');
    const injectionFilePath = path.join(this.projectPath, 'runtime-error-injection.js');
    await fs.writeFile(injectionFilePath, injectionScript, 'utf8');

    console.log(chalk.gray(`  📝 错误监控脚本已生成: ${injectionFilePath}`));
    console.log(chalk.gray('  ✅ 错误监控代码注入完成'));

    return injectionFilePath;
  }

  /**
   * 启动开发服务器
   */
  async startDevServer() {
    console.log(chalk.gray('  🌐 启动开发服务器...'));

    try {
      // 设置环境变量，让开发服务器知道要注入错误监控
      process.env.RUNTIME_ERROR_MONITORING = 'true';
      process.env.RUNTIME_ERROR_ENDPOINT = this.runtimeErrorHandler.options.errorEndpoint;

      // 启动开发服务器（非阻塞）
      const devProcess = await this.buildExecutor.startDevServer({
        port: this.options.port,
        setupMiddleware: (app) => {
          // 添加运行时错误处理路由
          app.use(this.runtimeErrorHandler.getRouter());
        }
      });

      // 等待一下让服务器完全启动
      await new Promise(resolve => setTimeout(resolve, 3000));

      // 注入错误监控代码到运行中的页面
      await this.injectErrorMonitoringToRunningServer();

      console.log(chalk.green(`  ✅ 开发服务器已启动 (端口: ${this.options.port})`));

      return devProcess;

    } catch (error) {
      throw new Error(`启动开发服务器失败: ${error.message}`);
    }
  }

  /**
   * 向运行中的服务器注入错误监控代码
   */
  async injectErrorMonitoringToRunningServer() {
    try {
      const axios = require('axios');
      const serverUrl = `http://localhost:${this.options.port}`;

      console.log(chalk.gray(`  🔌 向服务器注入错误监控代码: ${serverUrl}`));

      // 尝试访问首页来触发页面加载
      try {
        await axios.get(serverUrl, { timeout: 5000 });
        console.log(chalk.gray('  📄 页面加载成功，错误监控已激活'));
      } catch (error) {
        console.log(chalk.yellow(`  ⚠️  无法访问页面: ${error.message}`));
      }

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  注入错误监控代码失败: ${error.message}`));
    }
  }

  /**
   * 监控运行时错误
   */
  async monitorRuntimeErrors() {
    console.log(chalk.blue(`  👀 开始监控运行时错误 (${this.options.timeout / 1000}秒)...`));

    const startTime = Date.now();
    const endTime = startTime + this.options.timeout;

    // 如果是测试模式，模拟一些错误
    if (this.options.simulateErrors !== false) {
      await this.simulateTestErrors();
    }

    return new Promise((resolve) => {
      const checkInterval = setInterval(() => {
        const now = Date.now();
        const stats = this.runtimeErrorHandler.getErrorStats();

        // 更新统计信息
        this.stats.totalErrors = stats.totalErrors;
        this.stats.fixedErrors = stats.fixedErrors;
        this.stats.pendingErrors = stats.pendingErrors;

        // 输出进度信息
        if (this.options.verbose && stats.totalErrors > 0) {
          const elapsed = Math.floor((now - startTime) / 1000);
          console.log(chalk.gray(
            `    [${elapsed}s] 错误: ${stats.totalErrors}, 已修复: ${stats.fixedErrors}, 待处理: ${stats.pendingErrors}`
          ));
        }

        // 检查是否超时
        if (now >= endTime) {
          clearInterval(checkInterval);

          console.log(chalk.blue('  ⏰ 监控时间结束'));

          resolve({
            monitoringCompleted: true,
            duration: now - startTime,
            errorsDetected: stats.totalErrors,
            errorsFixed: stats.fixedErrors
          });
        }
      }, 1000); // 每秒检查一次
    });
  }

  /**
   * 模拟测试错误
   */
  async simulateTestErrors() {
    console.log(chalk.gray('  🧪 模拟运行时错误进行测试...'));

    try {
      const RuntimeErrorSimulator = require('./RuntimeErrorSimulator');
      const simulator = new RuntimeErrorSimulator(
        `http://localhost:${this.options.port}`,
        {
          errorEndpoint: this.runtimeErrorHandler.options.errorEndpoint,
          verbose: this.options.verbose
        }
      );

      // 等待2秒让服务器稳定
      await new Promise(resolve => setTimeout(resolve, 2000));

      // 检查错误端点是否可用
      const endpointCheck = await simulator.checkErrorEndpoint();
      if (!endpointCheck.available) {
        console.log(chalk.yellow('  ⚠️  错误监控端点不可用，跳过错误模拟'));
        return;
      }

      // 模拟几个不同类型的错误
      console.log(chalk.gray('  🔥 发送模拟错误...'));

      await simulator.simulateVueReactiveError();
      await new Promise(resolve => setTimeout(resolve, 1000));

      await simulator.simulateTemplateError();
      await new Promise(resolve => setTimeout(resolve, 1000));

      await simulator.simulateEventHandlerError();

      console.log(chalk.gray('  ✅ 模拟错误发送完成'));

    } catch (error) {
      console.log(chalk.yellow(`  ⚠️  错误模拟失败: ${error.message}`));
    }
  }

  /**
   * 停止开发服务器
   */
  async stopDevServer(devProcess) {
    console.log(chalk.gray('  🛑 停止开发服务器...'));

    if (devProcess && devProcess.kill) {
      devProcess.kill('SIGTERM');

      // 等待进程结束
      await new Promise((resolve) => {
        devProcess.on('exit', resolve);
        setTimeout(resolve, 5000); // 5秒超时
      });
    }

    console.log(chalk.gray('  ✅ 开发服务器已停止'));
  }

  /**
   * 生成修复报告
   */
  generateReport() {
    const duration = Math.max(0, this.stats.endTime - this.stats.startTime);
    const fixRate = this.stats.totalErrors > 0
      ? (this.stats.fixedErrors / this.stats.totalErrors * 100).toFixed(1)
      : 0;

    const report = {
      summary: {
        duration: `${Math.floor(duration / 1000)}秒`,
        totalErrors: this.stats.totalErrors,
        fixedErrors: this.stats.fixedErrors,
        pendingErrors: this.stats.pendingErrors,
        fixRate: `${fixRate}%`
      },
      details: {
        monitoringStarted: new Date(this.stats.startTime).toISOString(),
        monitoringEnded: new Date(this.stats.endTime).toISOString(),
        autoFixEnabled: this.options.autoFix
      }
    };

    // 输出报告
    console.log(chalk.blue('\n📊 运行时错误修复报告:'));
    console.log(chalk.gray(`  监控时长: ${report.summary.duration}`));
    console.log(chalk.gray(`  检测错误: ${report.summary.totalErrors} 个`));
    console.log(chalk.gray(`  修复错误: ${report.summary.fixedErrors} 个`));
    console.log(chalk.gray(`  待处理错误: ${report.summary.pendingErrors} 个`));
    console.log(chalk.gray(`  修复成功率: ${report.summary.fixRate}`));

    return report;
  }

  /**
   * 获取阶段依赖
   */
  getDependencies() {
    return ['构建时错误修复']; // 依赖构建时修复阶段
  }

  /**
   * 获取关键依赖
   */
  getCriticalDependencies() {
    return []; // 运行时修复不是关键依赖
  }

  /**
   * 判断是否为关键错误
   */
  isCriticalError() {
    // 运行时错误修复失败不应该阻止后续阶段
    return false;
  }
}

module.exports = RuntimeErrorRepairPhase;