const axios = require('axios');
const chalk = require('chalk');

/**
 * RuntimeErrorSimulator - 运行时错误模拟器
 *
 * 用于测试运行时错误监控和修复功能
 * 可以向运行中的Vue应用注入各种类型的错误
 */
class RuntimeErrorSimulator {
  constructor(serverUrl, options = {}) {
    this.serverUrl = serverUrl;
    this.options = {
      errorEndpoint: '/__runtime_errors__',
      verbose: false,
      ...options
    };
  }

  /**
   * 模拟Vue响应式错误
   */
  async simulateVueReactiveError() {
    const errorData = {
      message: 'Cannot read properties of undefined (reading \'value\')',
      fileName: 'src/components/TestComponent.vue',
      lineNumber: 25,
      columnNumber: 15,
      stack: `TypeError: Cannot read properties of undefined (reading 'value')
    at Proxy.setup (TestComponent.vue:25:15)
    at callWithErrorHandling (runtime-core.esm-bundler.js:155:22)
    at setupStatefulComponent (runtime-core.esm-bundler.js:7151:29)`,
      componentTrace: [
        { name: 'TestComponent', file: 'src/components/TestComponent.vue', line: 25 },
        { name: 'App', file: 'src/App.vue', line: 10 }
      ],
      timestamp: new Date().toISOString(),
      type: 'vue-error',
      vueInfo: 'setup function'
    };

    return this.sendError(errorData);
  }

  /**
   * 模拟组件生命周期错误
   */
  async simulateLifecycleError() {
    const errorData = {
      message: 'Cannot access before initialization',
      fileName: 'src/components/LifecycleComponent.vue',
      lineNumber: 18,
      columnNumber: 8,
      stack: `ReferenceError: Cannot access 'data' before initialization
    at mounted (LifecycleComponent.vue:18:8)
    at callWithErrorHandling (runtime-core.esm-bundler.js:155:22)`,
      componentTrace: [
        { name: 'LifecycleComponent', file: 'src/components/LifecycleComponent.vue', line: 18 }
      ],
      timestamp: new Date().toISOString(),
      type: 'vue-error',
      vueInfo: 'mounted hook'
    };

    return this.sendError(errorData);
  }

  /**
   * 模拟模板渲染错误
   */
  async simulateTemplateError() {
    const errorData = {
      message: 'Cannot read properties of null (reading \'name\')',
      fileName: 'src/views/UserProfile.vue',
      lineNumber: 12,
      columnNumber: 20,
      stack: `TypeError: Cannot read properties of null (reading 'name')
    at Proxy.render (UserProfile.vue:12:20)
    at renderComponentRoot (runtime-core.esm-bundler.js:896:44)`,
      componentTrace: [
        { name: 'UserProfile', file: 'src/views/UserProfile.vue', line: 12 },
        { name: 'RouterView', file: 'node_modules/vue-router/dist/vue-router.esm-bundler.js', line: 0 }
      ],
      timestamp: new Date().toISOString(),
      type: 'vue-error',
      vueInfo: 'render function'
    };

    return this.sendError(errorData);
  }

  /**
   * 模拟事件处理错误
   */
  async simulateEventHandlerError() {
    const errorData = {
      message: 'handleClick is not defined',
      fileName: 'src/components/Button.vue',
      lineNumber: 8,
      columnNumber: 25,
      stack: `ReferenceError: handleClick is not defined
    at HTMLButtonElement.onClick (Button.vue:8:25)
    at callWithErrorHandling (runtime-core.esm-bundler.js:155:22)`,
      componentTrace: [
        { name: 'CustomButton', file: 'src/components/Button.vue', line: 8 }
      ],
      timestamp: new Date().toISOString(),
      type: 'vue-error',
      vueInfo: 'click handler'
    };

    return this.sendError(errorData);
  }

  /**
   * 模拟异步操作错误
   */
  async simulateAsyncError() {
    const errorData = {
      message: 'Network request failed',
      fileName: 'src/services/api.js',
      lineNumber: 45,
      columnNumber: 12,
      stack: `Error: Network request failed
    at fetchUserData (api.js:45:12)
    at async setup (UserComponent.vue:20:18)`,
      componentTrace: [
        { name: 'UserComponent', file: 'src/components/UserComponent.vue', line: 20 }
      ],
      timestamp: new Date().toISOString(),
      type: 'promise-rejection'
    };

    return this.sendError(errorData);
  }

  /**
   * 模拟JavaScript类型错误
   */
  async simulateTypeError() {
    const errorData = {
      message: 'Cannot read properties of undefined (reading \'length\')',
      fileName: 'src/utils/helpers.js',
      lineNumber: 15,
      columnNumber: 28,
      stack: `TypeError: Cannot read properties of undefined (reading 'length')
    at processArray (helpers.js:15:28)
    at setup (DataTable.vue:35:20)`,
      componentTrace: [
        { name: 'DataTable', file: 'src/components/DataTable.vue', line: 35 }
      ],
      timestamp: new Date().toISOString(),
      type: 'javascript-error'
    };

    return this.sendError(errorData);
  }

  /**
   * 发送错误到服务器
   */
  async sendError(errorData) {
    try {
      const url = `${this.serverUrl}${this.options.errorEndpoint}`;

      if (this.options.verbose) {
        console.log(chalk.gray(`📤 发送模拟错误: ${errorData.message}`));
        console.log(chalk.gray(`   文件: ${errorData.fileName}:${errorData.lineNumber}`));
        console.log(chalk.gray(`   类型: ${errorData.type}`));
      }

      const response = await axios.post(url, errorData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 5000
      });

      if (this.options.verbose) {
        console.log(chalk.green(`✅ 错误发送成功: ${response.status}`));
      }

      return { success: true, response: response.data };

    } catch (error) {
      if (this.options.verbose) {
        console.log(chalk.red(`❌ 错误发送失败: ${error.message}`));
      }
      return { success: false, error: error.message };
    }
  }

  /**
   * 运行完整的错误模拟测试
   */
  async runFullErrorSimulation() {
    console.log(chalk.blue('🧪 开始运行时错误模拟测试...'));

    const errors = [
      { name: 'Vue响应式错误', fn: () => this.simulateVueReactiveError() },
      { name: '生命周期错误', fn: () => this.simulateLifecycleError() },
      { name: '模板渲染错误', fn: () => this.simulateTemplateError() },
      { name: '事件处理错误', fn: () => this.simulateEventHandlerError() },
      { name: '异步操作错误', fn: () => this.simulateAsyncError() },
      { name: 'JavaScript类型错误', fn: () => this.simulateTypeError() }
    ];

    const results = [];

    for (const error of errors) {
      console.log(chalk.gray(`  🔥 模拟${error.name}...`));

      const result = await error.fn();
      results.push({ name: error.name, ...result });

      // 等待一下，避免错误发送过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 统计结果
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;

    console.log(chalk.blue('\n📊 错误模拟结果:'));
    console.log(chalk.gray(`  成功发送: ${chalk.green(successful)} 个错误`));
    console.log(chalk.gray(`  发送失败: ${chalk.red(failed)} 个错误`));

    if (failed > 0) {
      console.log(chalk.yellow('\n⚠️  失败的错误:'));
      results.filter(r => !r.success).forEach(r => {
        console.log(chalk.gray(`    ${r.name}: ${r.error}`));
      });
    }

    return {
      total: results.length,
      successful,
      failed,
      results
    };
  }

  /**
   * 检查错误监控端点是否可用
   */
  async checkErrorEndpoint() {
    try {
      const url = `${this.serverUrl}${this.options.errorEndpoint}`;
      const response = await axios.get(url, { timeout: 30000 });

      console.log(chalk.green(`✅ 错误监控端点可用: ${url}`));
      return { available: true, response: response.data };

    } catch (error) {
      console.log(chalk.red(`❌ 错误监控端点不可用: ${error.message}`));
      return { available: false, error: error.message };
    }
  }
}

module.exports = RuntimeErrorSimulator;
